/* eslint-disable no-console */
import {
  Raydium,
  parseTokenAccountResp,
  TxVersion,
  ApiV3PoolInfoConcentratedItem,
  ClmmKeys,
  PoolUtils,
} from '@inclusivelayer/raydium-sdk-v2'
import { TOKEN_PROGRAM_ID, TOKEN_2022_PROGRAM_ID } from '@solana/spl-token'
import { Connection, Keypair, LAMPORTS_PER_SOL, PublicKey } from '@solana/web3.js'
import BN from 'bn.js'
import bs58 from 'bs58'
import Decimal from 'decimal.js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

// Script-specific configurations
export const SCRIPT_CONFIGS = {
  'project-manager': {
    USDC_AMOUNT: '6000000', // 6 USDC (6 decimals)
    DESCRIPTION: 'Project Manager Method',
  },
  'from-liquidity': {
    USDC_AMOUNT: '********', // 10 USDC (6 decimals)
    DESCRIPTION: 'From Liquidity Method',
  },
  'from-base': {
    USDC_AMOUNT: '5000000', // 5 USDC (6 decimals)
    DESCRIPTION: 'From Base Method',
  },
} as const

export type ScriptType = keyof typeof SCRIPT_CONFIGS

// Common configuration - Update these values for your setup
export const CONFIG = {
  // RPC endpoint
  RPC_URL: 'https://mainnet.helius-rpc.com/?api-key=d4d3c545-bd81-405c-9e51-3f600e9c25ad',

  // Wallet private key (base58 encoded from Phantom) - loaded from env
  PRIVATE_KEY: process.env.PRIVATE_KEY || '', // Set PRIVATE_KEY in .env file

  // Pool configuration - REQUIRED: Update these with your actual values
  POOL_ID: 'A7Uj6P274KY5tQWNwzv4X2HfjByz5Ehb3CrAvLV8h7nf', // Replace with your pool ID
  TOKEN_A_MINT: 'DN6TnqyLpnR5zL7Vvp2xYMoPz9uchSgu9RZocwThc52M', // Test token mint (9 decimals)
  TOKEN_B_MINT: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC mint (6 decimals)

  // Liquidity amounts (in base units)
  TOKEN_A_AMOUNT: '************', // 420 Test Tokens (9 decimals)
  TOKEN_B_AMOUNT: '6000000', // 6 USDC (6 decimals)

  // Transaction settings
  TX_VERSION: TxVersion.V0, // or TxVersion.LEGACY
  COMPUTE_UNIT_PRICE: 100000, // microLamports
  SLIPPAGE: 0.01, // 1% default slippage
}

export interface LiquidityParams {
  poolId: string
  slippage?: number
  scriptType: ScriptType
}

export interface TokenAnalysis {
  isTokenAUsdc: boolean
  isTokenBUsdc: boolean
  usdcMintInfo: { address: string; decimals: number }
  usdcDisplay: string
  testTokenDisplay: string
  usdcAmount: BN
}

export interface LiquidityCalculation {
  tokenAnalysis: TokenAnalysis
  calculatedTestTokenAmount: BN
  amountMaxA: BN
  amountMaxB: BN
}

export abstract class RaydiumBase {
  protected connection: Connection
  protected wallet: Keypair
  protected raydium: Raydium | null = null

  constructor() {
    this.connection = new Connection(CONFIG.RPC_URL, 'confirmed')
    this.wallet = this.loadKeypair()
  }

  private loadKeypair(): Keypair {
    try {
      if (!CONFIG.PRIVATE_KEY) {
        throw new Error('Please set PRIVATE_KEY in your .env file with your private key from Phantom wallet')
      }

      // Decode the base58 private key from Phantom
      const secretKey = bs58.decode(CONFIG.PRIVATE_KEY)
      const keypair = Keypair.fromSecretKey(secretKey)

      console.log(`🔑 Wallet loaded: ${keypair.publicKey.toString()}`)

      return keypair
    } catch (error) {
      console.error('❌ Error loading private key:', error)
      throw new Error("Failed to load private key. Make sure it's a valid base58 private key from Phantom.")
    }
  }

  protected async initializeRaydium(): Promise<void> {
    try {
      console.log('🔄 Initializing Raydium SDK...')

      // Get token account data
      const solAccountResp = await this.connection.getAccountInfo(this.wallet.publicKey)
      const tokenAccountResp = await this.connection.getTokenAccountsByOwner(this.wallet.publicKey, {
        programId: TOKEN_PROGRAM_ID,
      })
      const token2022Req = await this.connection.getTokenAccountsByOwner(this.wallet.publicKey, {
        programId: TOKEN_2022_PROGRAM_ID,
      })

      const tokenAccountData = parseTokenAccountResp({
        owner: this.wallet.publicKey,
        solAccountResp,
        tokenAccountResp: {
          context: tokenAccountResp.context,
          value: [...tokenAccountResp.value, ...token2022Req.value],
        },
      })

      // Initialize Raydium
      this.raydium = await Raydium.load({
        connection: this.connection,
        owner: this.wallet,
        cluster: 'mainnet', // or 'devnet'/'testnet' depending on your fork
        disableLoadToken: false,
        tokenAccounts: tokenAccountData.tokenAccounts,
        tokenAccountRawInfos: tokenAccountData.tokenAccountRawInfos,
      })

      console.log('✅ Raydium SDK initialized successfully!')
      console.log(`🔑 Wallet: ${this.wallet.publicKey.toString()}`)
    } catch (error) {
      console.error('❌ Error initializing Raydium SDK:', error)
      throw error
    }
  }

  protected async checkWalletBalance(): Promise<void> {
    try {
      const balance = await this.connection.getBalance(this.wallet.publicKey)
      const solBalance = balance / LAMPORTS_PER_SOL

      console.log(`💰 SOL Balance: ${solBalance.toFixed(4)} SOL`)

      if (solBalance < 0.01) {
        console.warn('⚠️  Low SOL balance! Make sure you have enough SOL for transactions.')
      }

      // Check token accounts
      const tokenAccounts = this.raydium?.account.tokenAccounts || []
      console.log(`🪙  Token Accounts: ${tokenAccounts.length} found`)

      tokenAccounts.forEach((account) => {
        console.log(`   - ${account.mint.toString()}: ${account.amount.toString()}`)
      })
    } catch (error) {
      console.error('❌ Error checking wallet balance:', error)
      throw error
    }
  }

  protected async getPoolInfo(
    poolId: string,
  ): Promise<{ poolInfo: ApiV3PoolInfoConcentratedItem; poolKeys: ClmmKeys }> {
    try {
      console.log(`🔍 Fetching CLMM pool info via RPC for: ${poolId}`)

      // Use the correct method from the demo that returns properly typed data
      const { poolInfo, poolKeys } = await this.raydium!.clmm.getPoolInfoFromRpc(poolId)

      if (!poolInfo || !poolKeys) {
        throw new Error(`Pool ${poolId} not found via RPC`)
      }

      console.log('✅ Pool found via RPC method')
      console.log('📊 CLMM Pool Info:')
      console.log(`   - Pool ID: ${poolId}`)
      console.log(`   - Token A: ${poolInfo.mintA.address}`)
      console.log(`   - Token B: ${poolInfo.mintB.address}`)
      console.log(`   - Decimals A/B: ${poolInfo.mintA.decimals}/${poolInfo.mintB.decimals}`)
      console.log(`   - Current Price: ${poolInfo.price}`)

      return { poolInfo, poolKeys }
    } catch (error) {
      console.error('❌ Error fetching CLMM pool info:', error)
      throw error
    }
  }

  protected validateConfiguration(): void {
    if (CONFIG.POOL_ID === 'YOUR_POOL_ID_HERE') {
      throw new Error('POOL_ID is required. Please update CONFIG.POOL_ID with your actual pool ID.')
    }

    if (CONFIG.TOKEN_B_MINT === 'YOUR_TOKEN_MINT_HERE') {
      throw new Error('TOKEN_B_MINT is required. Please update CONFIG.TOKEN_B_MINT with your actual token mint.')
    }
  }

  /**
   * Fetches token metadata (name, symbol) from the Metaplex Token Metadata program
   */
  protected async getTokenMetadata(mintAddress: string): Promise<{ name: string; symbol: string } | null> {
    try {
      const mint = new PublicKey(mintAddress)

      // Derive the metadata account PDA
      const METADATA_PROGRAM_ID = new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s')
      const [metadataAccount] = await PublicKey.findProgramAddress(
        [Buffer.from('metadata'), METADATA_PROGRAM_ID.toBuffer(), mint.toBuffer()],
        METADATA_PROGRAM_ID,
      )

      // Check if metadata account exists
      const metadataAccountInfo = await this.connection.getAccountInfo(metadataAccount)

      if (!metadataAccountInfo) {
        console.log(`   ⚠️  No metadata found for mint: ${mintAddress}`)

        return null
      }

      // Parse the metadata (simplified parsing for name and symbol)
      const { data } = metadataAccountInfo

      // Skip the initial metadata key (1 byte) and other fields to get to the data
      let offset = 1 + 32 + 32 // key + updateAuthority + mint

      // Read name length and name
      const nameLength = data.readUInt32LE(offset)
      offset += 4
      const name = data
        .subarray(offset, offset + nameLength)
        .toString('utf8')
        .replace(/\0/g, '')
      offset += nameLength

      // Read symbol length and symbol
      const symbolLength = data.readUInt32LE(offset)
      offset += 4
      const symbol = data
        .subarray(offset, offset + symbolLength)
        .toString('utf8')
        .replace(/\0/g, '')

      return { name: name.trim(), symbol: symbol.trim() }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.log(`   ⚠️  Error fetching metadata for ${mintAddress}:`, errorMessage)

      return null
    }
  }

  /**
   * Analyzes pool tokens to determine which is USDC and creates display names
   */
  protected async analyzePoolTokens(
    poolInfo: ApiV3PoolInfoConcentratedItem,
    scriptType: ScriptType,
  ): Promise<TokenAnalysis> {
    // Dynamically determine which token is USDC
    const USDC_MINT = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
    const isTokenAUsdc = poolInfo.mintA.address === USDC_MINT
    const isTokenBUsdc = poolInfo.mintB.address === USDC_MINT

    if (!isTokenAUsdc && !isTokenBUsdc) {
      throw new Error('Neither token in the pool is USDC. Please check the pool configuration.')
    }

    // Fetch token metadata for display purposes
    const tokenAMetadata = await this.getTokenMetadata(poolInfo.mintA.address)
    const tokenADisplay = tokenAMetadata
      ? `${tokenAMetadata.symbol} (${tokenAMetadata.name})`
      : `Token A (${poolInfo.mintA.address.slice(0, 8)}...)`

    const tokenBMetadata = await this.getTokenMetadata(poolInfo.mintB.address)
    const tokenBDisplay = tokenBMetadata
      ? `${tokenBMetadata.symbol} (${tokenBMetadata.name})`
      : `Token B (${poolInfo.mintB.address.slice(0, 8)}...)`

    // Determine USDC and test token info
    const usdcMintInfo = isTokenAUsdc ? poolInfo.mintA : poolInfo.mintB
    const usdcDisplay = isTokenAUsdc ? tokenADisplay : tokenBDisplay
    const testTokenDisplay = isTokenAUsdc ? tokenBDisplay : tokenADisplay

    console.log(`🔍 Pool Analysis:`)
    console.log(`   - USDC Token: ${usdcDisplay} (${isTokenAUsdc ? 'Token A' : 'Token B'})`)
    console.log(`   - Test Token: ${testTokenDisplay} (${isTokenAUsdc ? 'Token B' : 'Token A'})`)

    // Get the configured USDC amount for this script type
    const usdcAmount = new BN(SCRIPT_CONFIGS[scriptType].USDC_AMOUNT)

    return {
      isTokenAUsdc,
      isTokenBUsdc,
      usdcMintInfo,
      usdcDisplay,
      testTokenDisplay,
      usdcAmount,
    }
  }

  /**
   * Calculates liquidity amounts based on USDC input
   */
  protected async calculateLiquidityAmounts(
    poolInfo: ApiV3PoolInfoConcentratedItem,
    tokenAnalysis: TokenAnalysis,
    tickLower: number,
    tickUpper: number,
    slippage: number,
  ): Promise<LiquidityCalculation> {
    console.log(`💱 Calculating liquidity amounts:`)
    console.log(
      `   - USDC Amount: ${new Decimal(tokenAnalysis.usdcAmount.toString())
        .div(10 ** tokenAnalysis.usdcMintInfo.decimals)
        .toString()} ${tokenAnalysis.usdcDisplay}`,
    )
    console.log(`   - Slippage: ${(slippage * 100).toFixed(2)}%`)

    const epochInfo = await this.raydium!.fetchEpochInfo()

    // Calculate liquidity based on USDC input to ensure we deposit exactly the specified USDC amount
    const res = await PoolUtils.getLiquidityAmountOutFromAmountIn({
      poolInfo,
      slippage: 0,
      inputA: tokenAnalysis.isTokenAUsdc, // Input based on which token is USDC
      tickUpper,
      tickLower,
      amount: tokenAnalysis.usdcAmount,
      add: true,
      amountHasFee: true,
      epochInfo,
    })

    // Calculate required amounts based on which token is USDC
    const calculatedTestTokenAmount = new BN(
      new Decimal(
        tokenAnalysis.isTokenAUsdc ? res.amountSlippageB.amount.toString() : res.amountSlippageA.amount.toString(),
      )
        .mul(1 + slippage)
        .toFixed(0),
    )

    // Set up amounts for the transaction parameters
    const amountMaxA = tokenAnalysis.isTokenAUsdc ? tokenAnalysis.usdcAmount : calculatedTestTokenAmount
    const amountMaxB = tokenAnalysis.isTokenAUsdc ? calculatedTestTokenAmount : tokenAnalysis.usdcAmount

    // Check if user has enough USDC
    const usdcAccount = this.raydium!.account.tokenAccounts.find(
      (account) => account.mint.toString() === tokenAnalysis.usdcMintInfo.address,
    )

    if (!usdcAccount) {
      throw new Error(`No ${tokenAnalysis.usdcDisplay} token account found in wallet`)
    }

    const availableUsdc = new BN(usdcAccount.amount.toString())

    if (availableUsdc.lt(tokenAnalysis.usdcAmount)) {
      throw new Error(
        `Insufficient ${tokenAnalysis.usdcDisplay} balance. Required: ${new Decimal(tokenAnalysis.usdcAmount.toString())
          .div(10 ** tokenAnalysis.usdcMintInfo.decimals)
          .toString()}, Available: ${new Decimal(availableUsdc.toString())
          .div(10 ** tokenAnalysis.usdcMintInfo.decimals)
          .toString()}`,
      )
    }

    return {
      tokenAnalysis,
      calculatedTestTokenAmount,
      amountMaxA,
      amountMaxB,
    }
  }

  // Abstract method that each implementation must provide
  public abstract addLiquidity(params: LiquidityParams): Promise<string>

  public async run(scriptType: ScriptType = 'project-manager'): Promise<void> {
    try {
      console.log(`🎯 Starting Raydium Liquidity Test - ${SCRIPT_CONFIGS[scriptType].DESCRIPTION}...\n`)

      // Initialize components
      await this.initializeRaydium()
      await this.checkWalletBalance()

      // Validate configuration
      this.validateConfiguration()

      // Add liquidity
      const liquidityParams: LiquidityParams = {
        poolId: CONFIG.POOL_ID,
        slippage: CONFIG.SLIPPAGE,
        scriptType,
      }

      const txId = await this.addLiquidity(liquidityParams)

      console.log('\n🎉 Test completed successfully!')
      console.log(`🔗 View transaction: https://solscan.io/tx/${txId}`)
    } catch (error) {
      console.error('\n❌ Test failed:', error)
      process.exit(1)
    }
  }
}

// Helper function to display usage information
export function displayUsage(scriptName: string) {
  console.log(`
🔧 Raydium Liquidity Tester Usage:

Configuration:
  Edit the CONFIG object in scripts/common/raydium-base.ts with your values:
  - POOL_ID: Your pool ID (replace current value)
  - TOKEN_B_MINT: Your token mint address (replace current value)
  - TOKEN_A_AMOUNT: Amount of SOL in lamports (default: ************)
  - TOKEN_B_AMOUNT: Amount of your token in base units
  - RPC_URL: Solana RPC endpoint
  - PRIVATE_KEY: Set in .env file with your private key from Phantom wallet

Usage:
  npx ts-node scripts/${scriptName}

Note: Make sure your wallet has sufficient balance for both tokens and transaction fees.
  `)
}
