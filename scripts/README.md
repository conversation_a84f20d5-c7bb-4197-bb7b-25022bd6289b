# Raydium Liquidity Testing Script

This script allows you to test adding liquidity to your Raydium fork deployed on Solana using the Raydium SDK V2.

## Prerequisites

1. **Node.js** with TypeScript support
2. **Required dependencies** (already installed in this project):
   - `@raydium-io/raydium-sdk-v2`
   - `@solana/web3.js`
   - `@solana/spl-token`
   - `bn.js` and `@types/bn.js`

## Setup

1. **Configure the script**:
   - Open `scripts/add-liquidity-test.ts`
   - Edit the `CONFIG` object at the top of the file with your values

2. **Set up your wallet**:
   - Either provide a keypair file path via `KEYPAIR_PATH` in CONFIG
   - Or let the script generate a new keypair (make sure to fund it!)

3. **Fund your wallet**:
   - Ensure your wallet has sufficient SOL for transaction fees
   - Ensure your wallet has the tokens you want to add as liquidity

## Configuration

Update the following required values in the `CONFIG` object:

- `POOL_ID`: Your Raydium pool ID (replace 'YOUR_POOL_ID_HERE')
- `TOKEN_B_MINT`: The mint address of your custom token (replace 'YOUR_TOKEN_MINT_HERE')
- `TOKEN_A_AMOUNT`: Amount of Token A (SOL) in lamports
- `TOKEN_B_AMOUNT`: Amount of Token B in base units

## Usage

### Basic Usage

```bash
# First, edit the CONFIG object in scripts/add-liquidity-test.ts
# Then run:
npx ts-node scripts/add-liquidity-test.ts
```

### Help

```bash
npx ts-node scripts/add-liquidity-test.ts --help
```

## Example

Edit the CONFIG object in `scripts/add-liquidity-test.ts`:

```typescript
const CONFIG = {
  RPC_URL: 'https://api.mainnet-beta.solana.com',
  KEYPAIR_PATH: path.join(process.cwd(), 'keypair.json'),
  
  POOL_ID: 'AVs9TA4nWDzfPJE9gGVNJMVhcQy3V9PGazuz33BfG2RA',
  TOKEN_A_MINT: 'So11111111111111111111111111111111111111112',
  TOKEN_B_MINT: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
  
  TOKEN_A_AMOUNT: '100000000',  // 0.1 SOL
  TOKEN_B_AMOUNT: '100000000',  // 100 tokens (adjust for decimals)
  
  TX_VERSION: TxVersion.V0,
  COMPUTE_UNIT_PRICE: 100000,
}
```

Then run:
```bash
npx ts-node scripts/add-liquidity-test.ts
```

## Important Notes

⚠️ **Before running on mainnet:**
- Test on devnet first
- Use small amounts for initial testing
- Ensure you understand the risks of adding liquidity
- Make sure your wallet has sufficient balance for both tokens and fees

🔐 **Security:**
- Keep your keypair file secure
- Don't commit keypair files to version control
- Use environment variables for sensitive data

📊 **Debugging:**
- The script provides detailed logging of each step
- Transaction IDs are displayed for verification on Solscan
- Wallet balances and token accounts are checked before execution

## Troubleshooting

### Common Issues

1. **"Pool not found" error**:
   - Verify your `POOL_ID` is correct
   - Ensure the pool exists on the network you're connecting to

2. **"Insufficient balance" error**:
   - Check your wallet has enough SOL for transaction fees
   - Verify you have enough of both tokens for the specified amounts

3. **"Token account not found" error**:
   - Ensure your wallet has token accounts for both tokens
   - The script will try to create them if needed

4. **RPC errors**:
   - Try using a different RPC endpoint
   - Check if the RPC endpoint supports the network you're testing on

### Getting Help

If you encounter issues:
1. Check the console output for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure your wallet is properly funded
4. Test with smaller amounts first

## Script Features

- ✅ Automatic keypair generation if not provided
- ✅ Wallet balance checking
- ✅ Token account validation
- ✅ Pool information fetching
- ✅ Slippage protection
- ✅ Comprehensive error handling
- ✅ Transaction confirmation and logging
- ✅ Support for both versioned and legacy transactions 