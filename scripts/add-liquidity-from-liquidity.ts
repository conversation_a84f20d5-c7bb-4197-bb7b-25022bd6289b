#!/usr/bin/env ts-node

/* eslint-disable no-console */

import { OpenPositionFromLiquidity, PoolUtils, TxVersion } from '@inclusivelayer/raydium-sdk-v2'
import Decimal from 'decimal.js'

import { RaydiumBase, LiquidityParams, CONFIG, displayUsage } from './common/raydium-base'

class RaydiumFromLiquidityTester extends RaydiumBase {
  public async addLiquidity(params: LiquidityParams): Promise<string> {
    try {
      console.log('🚀 Starting add liquidity process with openPositionFromLiquidity...')

      const { poolInfo, poolKeys } = await this.getPoolInfo(params.poolId)
      const slippage = params.slippage || 0.025 // 2.5% default slippage for liquidity method

      /** code below will get on chain realtime price to avoid slippage error, uncomment it if necessary */
      // const rpcData = await this.raydium!.clmm.getRpcClmmPoolInfo({ poolId: poolInfo.id })
      // poolInfo.price = rpcData.currentPrice

      await this.raydium!.account.fetchWalletTokenAccounts()

      console.log(
        `sol balance: ${new Decimal(this.raydium!.account.tokenAccounts.find((t) => t.isNative)?.amount.toString() ?? 0)
          .div(10 ** 9)
          .toDecimalPlaces(9)
          .toString()}`,
      )

      // Use centralized token analysis
      const tokenAnalysis = await this.analyzePoolTokens(poolInfo, params.scriptType)

      // Price range settings - using similar range to the example
      const startPrice = new Decimal('560.***************')
      const endPrice = new Decimal('647.**************')

      console.log(`   - Position Type: Price Range Liquidity`)
      console.log(`   - Current Price: ${poolInfo.price}`)
      console.log(`   - Price Range: ${startPrice} to ${endPrice}`)

      // Use exact tick values
      const finalLowerTick = -207244
      const finalUpperTick = 161189

      console.log(`   - Calculated Ticks: ${finalLowerTick} to ${finalUpperTick}`)

      // Use centralized liquidity calculation
      const liquidityCalc = await this.calculateLiquidityAmounts(
        poolInfo,
        tokenAnalysis,
        finalLowerTick,
        finalUpperTick,
        slippage,
      )

      const epochInfo = await this.raydium!.fetchEpochInfo()
      const res = await PoolUtils.getLiquidityAmountOutFromAmountIn({
        poolInfo,
        slippage: 0,
        inputA: tokenAnalysis.isTokenAUsdc,
        tickUpper: finalUpperTick,
        tickLower: finalLowerTick,
        amount: tokenAnalysis.usdcAmount,
        add: true,
        amountHasFee: true,
        epochInfo,
      })

      console.log('computed lp/amountA/amountB to ins:', {
        lp: res.liquidity.toString(),
        amountMaxA: `${new Decimal(liquidityCalc.amountMaxA.toString())
          .div(10 ** poolInfo.mintA.decimals)
          .toDecimalPlaces(poolInfo.mintA.decimals)
          .toString()} ${tokenAnalysis.isTokenAUsdc ? tokenAnalysis.usdcDisplay : tokenAnalysis.testTokenDisplay}`,
        amountMaxB: `${new Decimal(liquidityCalc.amountMaxB.toString())
          .div(10 ** poolInfo.mintB.decimals)
          .toDecimalPlaces(poolInfo.mintB.decimals)
          .toString()} ${tokenAnalysis.isTokenBUsdc ? tokenAnalysis.usdcDisplay : tokenAnalysis.testTokenDisplay}`,
      })

      // Prepare parameters for openPositionFromLiquidity
      const openPositionParams: OpenPositionFromLiquidity<TxVersion.V0> = {
        poolInfo,
        poolKeys,
        tickUpper: finalUpperTick,
        tickLower: finalLowerTick,
        liquidity: res.liquidity,
        amountMaxA: liquidityCalc.amountMaxA,
        amountMaxB: liquidityCalc.amountMaxB,
        ownerInfo: {
          useSOLBalance: false, // Changed to false since we're using USDC, not SOL
        },
        txVersion: TxVersion.V0,
        nft2022: true,
        computeBudgetConfig: {
          units: 600000,
          microLamports: CONFIG.COMPUTE_UNIT_PRICE,
        },
      }

      // Log all parameters being passed to openPositionFromLiquidity
      console.log('\n📋 Logging openPositionFromLiquidity parameters:')
      console.log(`   - poolInfo:`)
      console.log(`     • mintA: ${openPositionParams.poolInfo.mintA.address}`)
      console.log(`     • mintB: ${openPositionParams.poolInfo.mintB.address}`)
      console.log(`     • price: ${openPositionParams.poolInfo.price}`)
      console.log(`     • poolId: ${openPositionParams.poolInfo.id}`)
      console.log(`     • decimalsA: ${openPositionParams.poolInfo.mintA.decimals}`)
      console.log(`     • decimalsB: ${openPositionParams.poolInfo.mintB.decimals}`)
      console.log(`   - poolKeys:`)
      console.log(`     • id: ${openPositionParams.poolKeys?.id.toString() || 'undefined'}`)
      console.log(`     • programId: ${openPositionParams.poolKeys?.programId.toString() || 'undefined'}`)
      console.log(`     • mintA: ${openPositionParams.poolKeys?.mintA.toString() || 'undefined'}`)
      console.log(`     • mintB: ${openPositionParams.poolKeys?.mintB.toString() || 'undefined'}`)
      console.log(`     • tickSpacing: ${openPositionParams.poolKeys?.config.tickSpacing || 'undefined'}`)
      console.log(`   - tickUpper: ${openPositionParams.tickUpper}`)
      console.log(`   - tickLower: ${openPositionParams.tickLower}`)
      console.log(`   - liquidity: ${openPositionParams.liquidity.toString()}`)
      console.log(`   - amountMaxA: ${openPositionParams.amountMaxA.toString()}`)
      console.log(`   - amountMaxB: ${openPositionParams.amountMaxB.toString()}`)
      console.log(`   - ownerInfo:`)
      console.log(`     • useSOLBalance: ${openPositionParams.ownerInfo.useSOLBalance}`)
      console.log(`   - txVersion: ${openPositionParams.txVersion}`)
      console.log(`   - nft2022: ${openPositionParams.nft2022}`)
      console.log(`   - computeBudgetConfig:`)
      console.log(`     • units: ${openPositionParams.computeBudgetConfig.units}`)
      console.log(`     • microLamports: ${openPositionParams.computeBudgetConfig.microLamports}`)

      // Create CLMM position instruction using openPositionFromLiquidity
      const { execute, extInfo } = await this.raydium!.clmm.openPositionFromLiquidity(openPositionParams)

      console.log('📝 Executing transaction...')

      const { txId } = await execute({ sendAndConfirm: true })

      console.log('✅ Liquidity added successfully!')
      console.log(`🔗 Transaction ID: ${txId}`)
      console.log(`🎟️  NFT Mint: ${extInfo.address.nftMint.toBase58()}`)

      return txId
    } catch (error) {
      console.error('❌ Error adding liquidity:', error)
      throw error
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2)

  if (args.includes('--help') || args.includes('-h')) {
    displayUsage('add-liquidity-from-liquidity.ts')

    return
  }

  console.log('🎯 Using openPositionFromLiquidity method\n')
  const tester = new RaydiumFromLiquidityTester()
  await tester.run('from-liquidity')
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Unhandled error:', error)
    process.exit(1)
  })
}

export { RaydiumFromLiquidityTester }
