{"name": "wallet-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "NODE_ENV=production node dist/src/main", "lint": "eslint \"src/**/*.ts\" --fix", "lint:check": "eslint \"src/**/*.ts\"", "lint:fix": "eslint \"src/**/*.ts\" --fix", "lint:ci": "eslint \"src/**/*.ts\" --max-warnings 0", "type-check": "tsc --noEmit", "check": "pnpm run type-check && pnpm run lint:check && pnpm run format:check", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/s3-request-presigner": "^3.826.0", "@inclusivelayer/raydium-sdk-v2": "^0.0.2", "@knaadh/nestjs-drizzle-pg": "^1.2.0", "@nestjs/cache-manager": "3.0.1", "@nestjs/common": "11.0.20", "@nestjs/config": "4.0.2", "@nestjs/core": "11.0.20", "@nestjs/event-emitter": "^3.0.1", "@nestjs/mapped-types": "2.1.0", "@nestjs/platform-express": "11.0.20", "@nestjs/schedule": "5.0.1", "@nestjs/swagger": "11.1.3", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.2", "@types/bn.js": "^5.2.0", "axios": "1.8.4", "axios-retry": "4.5.0", "better-auth": "^1.2.8", "bignumber.js": "9.1.2", "bs58": "6.0.0", "cache-manager": "6.4.1", "cashramp": "^0.0.6", "class-transformer": "0.5.1", "class-validator": "0.14.1", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.1", "drizzle-zod": "^0.8.2", "express": "^5.1.0", "firebase-admin": "13.2.0", "grammy": "^1.36.1", "nanoid": "5.1.5", "nestjs-pino": "4.4.0", "nestjs-zod": "4.3.1", "pg": "^8.16.0", "pino-http": "10.4.0", "pino-pretty": "13.0.0", "reflect-metadata": "0.2.2", "twilio": "^5.7.0", "viem": "2.27.0", "zod": "^3.25.1"}, "devDependencies": {"@heyllog/eslint-config": "1.0.2", "@nestjs/cli": "11.0.5", "@nestjs/schematics": "11.0.2", "@nestjs/testing": "11.0.12", "@types/express": "^5.0.2", "@types/jest": "29.5.14", "@types/node": "22.13.10", "@types/pg": "^8.15.4", "@types/supertest": "6.0.2", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "drizzle-kit": "^0.31.1", "eslint": "8.57.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-airbnb-typescript": "18.0.0", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-filename-rules": "1.3.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-unused-imports": "3.2.0", "jest": "29.7.0", "prettier": "3.5.3", "source-map-support": "0.5.21", "supertest": "7.0.0", "ts-jest": "29.2.6", "ts-loader": "9.5.2", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "tsx": "^4.19.4", "typescript": "5.8.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}