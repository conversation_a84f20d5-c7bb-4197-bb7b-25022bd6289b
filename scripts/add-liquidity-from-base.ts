#!/usr/bin/env ts-node

/* eslint-disable no-console */

import { TickUtils, PoolUtils } from '@inclusivelayer/raydium-sdk-v2'
import Decimal from 'decimal.js'

import { RaydiumBase, LiquidityParams, CONFIG, displayUsage } from './common/raydium-base'

class RaydiumFromBaseTester extends RaydiumBase {
  public async addLiquidity(params: LiquidityParams): Promise<string> {
    try {
      console.log('🚀 Starting add liquidity process with openPositionFromBase...')

      const { poolInfo, poolKeys } = await this.getPoolInfo(params.poolId)
      const slippage = params.slippage || 0.01 // 1% default slippage

      // Use centralized token analysis
      const tokenAnalysis = await this.analyzePoolTokens(poolInfo, params.scriptType)

      // Set price range - using a wide range similar to full range
      const currentPrice = poolInfo.price
      const [startPrice, endPrice] = [0.000001, 100000] // Wide range

      console.log(`💱 Adding liquidity using openPositionFromBase:`)
      console.log(`   - Position Type: Wide Range Liquidity`)
      console.log(`   - Current Price: ${currentPrice}`)
      console.log(`   - Price Range: ${startPrice} to ${endPrice}`)

      // Calculate ticks from prices
      const { tick: lowerTick } = TickUtils.getPriceAndTick({
        poolInfo,
        price: new Decimal(startPrice),
        baseIn: true,
      })

      const { tick: upperTick } = TickUtils.getPriceAndTick({
        poolInfo,
        price: new Decimal(endPrice),
        baseIn: true,
      })

      const finalLowerTick = Math.min(lowerTick, upperTick)
      const finalUpperTick = Math.max(lowerTick, upperTick)

      console.log(`   - Calculated Ticks: ${finalLowerTick} to ${finalUpperTick}`)

      // Use centralized liquidity calculation
      const liquidityCalc = await this.calculateLiquidityAmounts(
        poolInfo,
        tokenAnalysis,
        finalLowerTick,
        finalUpperTick,
        slippage,
      )

      // Get epoch info for liquidity calculation
      const epochInfo = await this.raydium!.fetchEpochInfo()

      // Calculate the required amount for the other token
      const liquidityCalcForBase = await PoolUtils.getLiquidityAmountOutFromAmountIn({
        poolInfo,
        slippage,
        inputA: tokenAnalysis.isTokenAUsdc, // Input USDC
        tickUpper: finalUpperTick,
        tickLower: finalLowerTick,
        amount: tokenAnalysis.usdcAmount,
        add: true,
        amountHasFee: true,
        epochInfo,
      })

      console.log(`   - Calculated Other Amount: ${liquidityCalcForBase.amountSlippageB.amount.toString()}`)

      // Determine which mint to use as base - use USDC as the base
      const baseAmount = tokenAnalysis.usdcAmount
      const otherAmountMax = tokenAnalysis.isTokenAUsdc
        ? liquidityCalc.calculatedTestTokenAmount
        : liquidityCalc.calculatedTestTokenAmount

      // Prepare parameters for openPositionFromBase
      const openPositionParams = {
        poolInfo,
        poolKeys,
        tickUpper: finalUpperTick,
        tickLower: finalLowerTick,
        base: tokenAnalysis.isTokenAUsdc ? ('MintA' as const) : ('MintB' as const),
        ownerInfo: {
          useSOLBalance: false, // Changed to false since we're using USDC, not SOL
        },
        baseAmount,
        nft2022: true,
        otherAmountMax,
        txVersion: CONFIG.TX_VERSION,
        computeBudgetConfig: {
          units: 600000,
          microLamports: CONFIG.COMPUTE_UNIT_PRICE,
        },
      }

      // Log all parameters being passed to openPositionFromBase
      console.log('\n📋 Logging openPositionFromBase parameters:')
      console.log(`   - poolInfo:`)
      console.log(`     • mintA: ${openPositionParams.poolInfo.mintA.address}`)
      console.log(`     • mintB: ${openPositionParams.poolInfo.mintB.address}`)
      console.log(`     • price: ${openPositionParams.poolInfo.price}`)
      console.log(`     • poolId: ${openPositionParams.poolInfo.id}`)
      console.log(`     • decimalsA: ${openPositionParams.poolInfo.mintA.decimals}`)
      console.log(`     • decimalsB: ${openPositionParams.poolInfo.mintB.decimals}`)
      console.log(`   - poolKeys:`)
      console.log(`     • id: ${openPositionParams.poolKeys?.id.toString() || 'undefined'}`)
      console.log(`     • programId: ${openPositionParams.poolKeys?.programId.toString() || 'undefined'}`)
      console.log(`     • mintA: ${openPositionParams.poolKeys?.mintA.toString() || 'undefined'}`)
      console.log(`     • mintB: ${openPositionParams.poolKeys?.mintB.toString() || 'undefined'}`)
      console.log(`   - tickUpper: ${openPositionParams.tickUpper}`)
      console.log(`   - tickLower: ${openPositionParams.tickLower}`)
      console.log(`   - base: ${openPositionParams.base}`)
      console.log(`   - ownerInfo:`)
      console.log(`     • useSOLBalance: ${openPositionParams.ownerInfo.useSOLBalance}`)
      console.log(`   - baseAmount: ${openPositionParams.baseAmount.toString()}`)
      console.log(`   - otherAmountMax: ${openPositionParams.otherAmountMax.toString()}`)
      console.log(`   - txVersion: ${openPositionParams.txVersion}`)
      console.log(`   - computeBudgetConfig:`)
      console.log(`     • units: ${openPositionParams.computeBudgetConfig.units}`)
      console.log(`     • microLamports: ${openPositionParams.computeBudgetConfig.microLamports}`)

      // Create CLMM position instruction using openPositionFromBase
      const { execute, extInfo } = await this.raydium!.clmm.openPositionFromBase(openPositionParams)

      console.log('📝 Executing transaction...')

      const result = await execute({ sendAndConfirm: true })

      console.log('✅ Liquidity added successfully!')
      console.log(`🔗 Transaction ID: ${result.txId}`)
      console.log(`🎟️  NFT Mint: ${extInfo.nftMint.toBase58()}`)

      return result.txId
    } catch (error) {
      console.error('❌ Error adding liquidity:', error)
      throw error
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2)

  if (args.includes('--help') || args.includes('-h')) {
    displayUsage('add-liquidity-from-base.ts')

    return
  }

  console.log('🎯 Using openPositionFromBase method\n')

  const tester = new RaydiumFromBaseTester()
  await tester.run('from-base')
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Unhandled error:', error)
    process.exit(1)
  })
}

export { RaydiumFromBaseTester }
